<?php

namespace Kangangga\EasylinkSdk\Tests;

use Orchestra\Testbench\TestCase as Orchestra;

abstract class TestCase extends Orchestra
{
    protected function getPackageProviders($app)
    {
        return [
            \Kangangga\EasylinkSdk\EasylinkSdkServiceProvider::class,
        ];
    }

    protected function getEnvironmentSetUp($app)
    {
        // Setup default database to use sqlite :memory:
        $app['config']->set('database.default', 'testbench');
        $app['config']->set('database.connections.testbench', [
            'driver'   => 'sqlite',
            'database' => ':memory:',
            'prefix'   => '',
        ]);

        // Setup easylink database connection
        $app['config']->set('database.connections.easylink', [
            'driver'   => 'sqlite',
            'database' => ':memory:',
            'prefix'   => '',
        ]);

        $app['config']->set('database.connections.fingerspot', [
            'driver'   => 'sqlite',
            'database' => ':memory:',
            'prefix'   => '',
        ]);

        // Setup package config
        $app['config']->set('laravel-easylink.sdk.host', 'http://localhost:8080');
        $app['config']->set('laravel-easylink.sdk.serial_number', 'TEST123456');

        $app['config']->set('easylink', 'fingerspot');
    }

    protected function getLaravelVersion()
    {
        return (float) app()->version();
    }
}
