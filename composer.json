{"name": "kangangga/laravel-easylink", "description": "laravel library for communicating with Fingerspot EasyLink SDK.", "keywords": ["kangangga", "laravel-easylink"], "homepage": "https://github.com/kangangga/laravel-easylink", "license": "MIT", "type": "library", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.0", "illuminate/container": "^8.12|^9.0|^10.0|^11.0", "illuminate/contracts": "^8.12|^9.0|^10.0|^11.0", "illuminate/database": "^8.12|^9.0|^10.0|^11.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.9", "orchestra/testbench": "^6.0", "phpunit/phpunit": "^9.0"}, "minimum-stability": "dev", "prefer-stable": true, "autoload": {"psr-4": {"Kangangga\\EasylinkSdk\\": "src"}}, "autoload-dev": {"psr-4": {"Kangangga\\EasylinkSdk\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage"}, "config": {"sort-packages": true}, "extra": {"laravel": {"providers": ["Kangangga\\EasylinkSdk\\EasylinkSdkServiceProvider"], "aliases": {"EasylinkSdk": "Kangangga\\EasylinkSdk\\EasylinkSdkFacade"}}}}