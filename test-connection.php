<?php

/**
 * EasyLink Connection Test Script
 * 
 * This script tests the connection to your Fingerspot EasyLink device
 * with the configured IP address and ports.
 * 
 * Configuration:
 * - Device IP: **************
 * - Device Port: 5005
 * - Server Port: 7005
 */

require_once 'vendor/autoload.php';

use Kangangga\EasylinkSdk\EasylinkSdk;

// Configuration
$deviceHost = 'http://**************:5005';
$serialNumber = '1234567890'; // Replace with your actual serial number
$serverPort = '7005';

echo "=== EasyLink Connection Test ===\n";
echo "Device Host: {$deviceHost}\n";
echo "Serial Number: {$serialNumber}\n";
echo "Server Port: {$serverPort}\n";
echo "================================\n\n";

try {
    // Create SDK instance
    echo "1. Creating SDK instance...\n";
    $sdk = new EasylinkSdk($deviceHost, $serialNumber);
    echo "   ✓ SDK instance created successfully\n\n";

    // Test basic connectivity by getting device info
    echo "2. Testing device connectivity...\n";
    $deviceInfo = $sdk->device();
    
    if ($deviceInfo && $deviceInfo->get('Result')) {
        echo "   ✓ Device connection successful!\n";
        echo "   Device Info:\n";
        foreach ($deviceInfo->get('Data', []) as $key => $value) {
            echo "     - {$key}: {$value}\n";
        }
    } else {
        echo "   ✗ Device connection failed\n";
        echo "   Response: " . json_encode($deviceInfo->toArray()) . "\n";
    }
    echo "\n";

    // Test scan log retrieval
    echo "3. Testing scan log retrieval...\n";
    $scanLogs = $sdk->scanlogNew();
    
    if ($scanLogs && $scanLogs->get('Result')) {
        echo "   ✓ Scan log retrieval successful!\n";
        $logs = $scanLogs->get('Data', []);
        echo "   Found " . count($logs) . " scan log entries\n";
        
        if (count($logs) > 0) {
            echo "   Latest scan log:\n";
            $latestLog = $logs[0];
            foreach ($latestLog as $key => $value) {
                echo "     - {$key}: {$value}\n";
            }
        }
    } else {
        echo "   ✗ Scan log retrieval failed\n";
        echo "   Response: " . json_encode($scanLogs->toArray()) . "\n";
    }
    echo "\n";

} catch (Exception $e) {
    echo "   ✗ Error occurred: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
}

// Network connectivity test
echo "4. Testing network connectivity...\n";
$host = '**************';
$devicePort = 5005;
$serverPort = 7005;

// Test device port
echo "   Testing device port {$devicePort}...\n";
$connection = @fsockopen($host, $devicePort, $errno, $errstr, 5);
if ($connection) {
    echo "   ✓ Device port {$devicePort} is accessible\n";
    fclose($connection);
} else {
    echo "   ✗ Device port {$devicePort} is not accessible: {$errstr} ({$errno})\n";
}

// Test server port
echo "   Testing server port {$serverPort}...\n";
$connection = @fsockopen($host, $serverPort, $errno, $errstr, 5);
if ($connection) {
    echo "   ✓ Server port {$serverPort} is accessible\n";
    fclose($connection);
} else {
    echo "   ✗ Server port {$serverPort} is not accessible: {$errstr} ({$errno})\n";
}

echo "\n=== Test Complete ===\n";

// Configuration recommendations
echo "\nConfiguration Recommendations:\n";
echo "1. Add to your .env file:\n";
echo "   EASYLINK_SDK_HOST=http://**************:5005\n";
echo "   EASYLINK_SDK_SN=your_actual_serial_number\n";
echo "   EASYLINK_SERVER_PORT=7005\n\n";

echo "2. Make sure your device is:\n";
echo "   - Powered on and connected to the network\n";
echo "   - Accessible from this server\n";
echo "   - Configured with the correct IP address (**************)\n";
echo "   - Running the EasyLink service on port 5005\n\n";

echo "3. Check firewall settings:\n";
echo "   - Allow outbound connections to **************:5005\n";
echo "   - Allow inbound connections on port 7005 (if needed)\n\n";
