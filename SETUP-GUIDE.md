# EasyLink Setup Guide for IP **************

This guide provides step-by-step instructions for setting up the Laravel EasyLink SDK with your specific device configuration.

## Device Configuration

- **Device IP Address**: **************
- **Device Port**: 5005
- **Server Port**: 7005

## Step 1: Environment Configuration

Create or update your `.env` file with the following configuration:

```env
# EasyLink Device Configuration
EASYLINK_SDK_HOST=http://**************:5005
EASYLINK_SDK_SN=your_device_serial_number
EASYLINK_SERVER_PORT=7005

# Database Configuration (if using database features)
EASYLINK_DB_HOST=localhost
EASYLINK_DB_PORT=3306
EASYLINK_DB_DATABASE=fingerspot
EASYLINK_DB_USERNAME=your_db_username
EASYLINK_DB_PASSWORD=your_db_password
```

**Important**: Replace `your_device_serial_number` with the actual serial number of your Fingerspot device.

## Step 2: Network Verification

Before using the SDK, verify network connectivity:

### 2.1 Ping Test
```bash
ping **************
```

### 2.2 Port Connectivity Test
```bash
# Test device port
telnet ************** 5005

# Test server port
telnet ************** 7005
```

### 2.3 Using the Test Script
Run the provided test script to verify connectivity:

```bash
php test-connection.php
```

## Step 3: Basic Usage Examples

### 3.1 Initialize SDK
```php
<?php
require_once 'vendor/autoload.php';

use Kangangga\EasylinkSdk\EasylinkSdk;

$sdk = new EasylinkSdk('http://**************:5005', 'your_serial_number');
```

### 3.2 Test Device Connection
```php
// Get device information
$deviceInfo = $sdk->device();

if ($deviceInfo->get('Result')) {
    echo "Device connected successfully!\n";
    print_r($deviceInfo->get('Data'));
} else {
    echo "Connection failed: " . $deviceInfo->get('Message') . "\n";
}
```

### 3.3 Retrieve Attendance Logs
```php
// Get all scan logs
$allLogs = $sdk->scanlogAll();

// Get only new scan logs
$newLogs = $sdk->scanlogNew();

if ($newLogs->get('Result')) {
    $logs = $newLogs->get('Data');
    echo "Found " . count($logs) . " new scan logs\n";
    
    foreach ($logs as $log) {
        echo "PIN: {$log['PIN']}, Date: {$log['ScanDate']}\n";
    }
}
```

### 3.4 User Management
```php
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Create a new user
$user = new EasylinkUser(
    sn: 'your_serial_number',
    pin: '001',
    nama: 'John Doe',
    pwd: 'password123',
    rfid: 'RFID123',
    priv: 1,
    tmp: null
);

$response = $sdk->userSet($user);

if ($response->get('Result')) {
    echo "User created successfully!\n";
} else {
    echo "User creation failed: " . $response->get('Message') . "\n";
}
```

## Step 4: Laravel Integration

### 4.1 Using the Facade
```php
use EasylinkSdk;

// In your controller or service
public function getDeviceInfo()
{
    $deviceInfo = EasylinkSdk::device();
    return response()->json($deviceInfo->toArray());
}

public function getAttendanceLogs()
{
    $logs = EasylinkSdk::scanlogNew();
    return response()->json($logs->toArray());
}
```

### 4.2 Using Dependency Injection
```php
use Kangangga\EasylinkSdk\EasylinkSdk;

public function __construct(EasylinkSdk $easylinkSdk)
{
    $this->easylinkSdk = $easylinkSdk;
}

public function syncAttendance()
{
    $logs = $this->easylinkSdk->scanlogNew();
    
    if ($logs->get('Result')) {
        // Process attendance logs
        foreach ($logs->get('Data') as $log) {
            // Save to database or process as needed
        }
    }
}
```

## Step 5: Troubleshooting

### Common Issues and Solutions

#### 5.1 Connection Timeout
**Problem**: Cannot connect to device
**Solutions**:
- Verify device IP address (**************)
- Check if device is powered on
- Ensure network connectivity
- Verify firewall settings

#### 5.2 Port Access Denied
**Problem**: Cannot access ports 5005 or 7005
**Solutions**:
- Check firewall rules on both server and device
- Verify device port configuration
- Ensure no other services are using these ports

#### 5.3 Authentication Failed
**Problem**: Device rejects requests
**Solutions**:
- Verify serial number is correct
- Check device authentication settings
- Ensure device is in the correct mode

#### 5.4 Invalid Response
**Problem**: Receiving unexpected responses
**Solutions**:
- Check device firmware version
- Verify API compatibility
- Review device logs if available

### Debug Mode

Enable debug mode to see detailed HTTP requests:

```php
// Add this to see HTTP requests and responses
$sdk = new EasylinkSdk('http://**************:5005', 'your_serial_number');

// The SDK uses Laravel's HTTP client, so you can enable logging
// in your Laravel application's logging configuration
```

## Step 6: Production Considerations

### 6.1 Security
- Use HTTPS if supported by your device
- Implement proper authentication
- Restrict network access to the device
- Use strong passwords for database connections

### 6.2 Performance
- Implement caching for device information
- Use queues for bulk operations
- Monitor network latency
- Set appropriate timeouts

### 6.3 Monitoring
- Log all API calls
- Monitor device connectivity
- Set up alerts for connection failures
- Track attendance data synchronization

## Support

If you encounter issues:

1. Run the test script: `php test-connection.php`
2. Check network connectivity
3. Verify device configuration
4. Review Laravel logs
5. Check device documentation

For additional help, refer to:
- CONFIGURATION.md for detailed configuration options
- README.md for usage examples
- The test files for implementation examples
