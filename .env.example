# Laravel EasyLink SDK Configuration
# Copy this file to .env and update the values according to your setup

# =============================================================================
# EasyLink SDK Configuration
# =============================================================================

# EasyLink Device Host URL
# The base URL of your Fingerspot EasyLink device
# Example: http://*************:8080 or http://device.local:8080
EASYLINK_SDK_HOST=localhost

# EasyLink Device Serial Number
# The serial number of your Fingerspot device
# Example: Fio616231022480092
EASYLINK_SDK_SN=1234567890

# =============================================================================
# EasyLink Database Configuration
# =============================================================================

# Database Connection URL (optional)
# If provided, this will override individual database settings
# Example: mysql://username:password@host:port/database
EASYLINK_DB_URL=

# Database Host
# The hostname or IP address of your database server
EASYLINK_DB_HOST=localhost

# Database Port
# The port number for your database connection
EASYLINK_DB_PORT=3306

# Database Name
# The name of the database containing fingerspot/attendance data
EASYLINK_DB_DATABASE=fingerspot

# Database Username
# The username for database authentication
EASYLINK_DB_USERNAME=fingerspot

# Database Password
# The password for database authentication
EASYLINK_DB_PASSWORD=fingerspot

# Database Socket (optional)
# Unix socket path for local database connections
EASYLINK_DB_SOCKET=

# =============================================================================
# SSL Configuration (optional)
# =============================================================================

# MySQL SSL Certificate Authority file path
# Only needed if using SSL connections to MySQL
MYSQL_ATTR_SSL_CA=
