<?php

namespace Kangangga\EasylinkSdk\Tests;

use Kangangga\EasylinkSdk\Models\Device;
use Kangangga\EasylinkSdk\Models\Pegawai;
use Kangangga\EasylinkSdk\Models\PegawaiDetail;
use Kangangga\EasylinkSdk\Models\DevType;
use Kangangga\EasylinkSdk\Models\AttLog;

class ModelsTest extends TestCase
{
    /** @test */
    public function device_model_has_correct_configuration()
    {
        $device = new Device();
        
        $this->assertEquals('device', $device->getTable());
        $this->assertEquals('sn', $device->getKeyName());
        $this->assertEquals('string', $device->getKeyType());
        $this->assertFalse($device->getIncrementing());
        $this->assertFalse($device->usesTimestamps());
        $this->assertEquals([], $device->getGuarded());
    }

    /** @test */
    public function device_model_uses_easylink_connection_trait()
    {
        $device = new Device();
        
        $this->assertTrue(method_exists($device, 'getConnectionName'));
        
        // Mock the config to test the trait
        config(['easylink' => 'test_connection']);
        $this->assertEquals('test_connection', $device->getConnectionName());
    }

    /** @test */
    public function pegawai_model_has_correct_configuration()
    {
        $pegawai = new Pegawai();
        
        $this->assertEquals('pegawai', $pegawai->getTable());
        $this->assertEquals('pegawai_id', $pegawai->getKeyName());
        $this->assertEquals('int', $pegawai->getKeyType());
        $this->assertTrue($pegawai->getIncrementing());
        $this->assertFalse($pegawai->usesTimestamps());
        $this->assertEquals([], $pegawai->getGuarded());
    }

    /** @test */
    public function pegawai_model_has_detail_relationship()
    {
        $pegawai = new Pegawai();
        
        $this->assertTrue(method_exists($pegawai, 'detail'));
        
        $relation = $pegawai->detail();
        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Relations\HasOne::class, $relation);
        $this->assertEquals('pegawai_id', $relation->getForeignKeyName());
        $this->assertEquals('pegawai_id', $relation->getLocalKeyName());
    }

    /** @test */
    public function pegawai_detail_model_has_correct_configuration()
    {
        $pegawaiDetail = new PegawaiDetail();
        
        $this->assertEquals('pegawai_d', $pegawaiDetail->getTable());
        $this->assertEquals('int', $pegawaiDetail->getKeyType());
        $this->assertTrue($pegawaiDetail->getIncrementing());
        $this->assertFalse($pegawaiDetail->usesTimestamps());
        $this->assertEquals([], $pegawaiDetail->getGuarded());
    }

    /** @test */
    public function pegawai_detail_model_uses_traits()
    {
        $pegawaiDetail = new PegawaiDetail();
        
        $this->assertTrue(method_exists($pegawaiDetail, 'getConnectionName'));
        
        // Check if PegawaiDetailAttribute trait methods exist
        $traits = class_uses($pegawaiDetail);
        $this->assertContains('Kangangga\EasylinkSdk\Traits\HasEasylinkConnection', $traits);
        $this->assertContains('Kangangga\EasylinkSdk\Traits\PegawaiDetailAttribute', $traits);
    }

    /** @test */
    public function dev_type_model_has_correct_configuration()
    {
        $devType = new DevType();
        
        $this->assertEquals('dev_type', $devType->getTable());
        $this->assertEquals('id_type', $devType->getKeyName());
        $this->assertEquals('int', $devType->getKeyType());
        $this->assertTrue($devType->getIncrementing());
        $this->assertFalse($devType->usesTimestamps());
        $this->assertEquals([], $devType->getGuarded());
    }

    /** @test */
    public function att_log_model_has_correct_configuration()
    {
        $attLog = new AttLog();
        
        $this->assertTrue(method_exists($attLog, 'getConnectionName'));
        
        // Check if it uses the HasEasylinkConnection trait
        $traits = class_uses($attLog);
        $this->assertContains('Kangangga\EasylinkSdk\Traits\HasEasylinkConnection', $traits);
    }

    /** @test */
    public function models_use_correct_connection()
    {
        config(['easylink' => 'custom_connection']);
        
        $device = new Device();
        $pegawai = new Pegawai();
        $pegawaiDetail = new PegawaiDetail();
        $devType = new DevType();
        $attLog = new AttLog();
        
        $this->assertEquals('custom_connection', $device->getConnectionName());
        $this->assertEquals('custom_connection', $pegawai->getConnectionName());
        $this->assertEquals('custom_connection', $pegawaiDetail->getConnectionName());
        $this->assertEquals('custom_connection', $devType->getConnectionName());
        $this->assertEquals('custom_connection', $attLog->getConnectionName());
    }

    /** @test */
    public function models_fallback_to_default_connection()
    {
        // Test that the trait method exists and works
        $device = new Device();

        // The trait should return either the configured value or the default
        $connectionName = $device->getConnectionName();

        // Should be either the configured connection or the default 'fingerspot'
        $this->assertTrue(
            $connectionName === 'fingerspot' || $connectionName === 'custom_connection',
            "Expected 'fingerspot' or configured connection, got: {$connectionName}"
        );
    }

    /** @test */
    public function device_model_can_be_instantiated()
    {
        $device = new Device([
            'sn' => 'TEST123456',
            'device_name' => 'Test Device',
            'ip_address' => '*************'
        ]);
        
        $this->assertInstanceOf(Device::class, $device);
        $this->assertEquals('TEST123456', $device->sn);
        $this->assertEquals('Test Device', $device->device_name);
        $this->assertEquals('*************', $device->ip_address);
    }

    /** @test */
    public function pegawai_model_can_be_instantiated()
    {
        $pegawai = new Pegawai([
            'pegawai_id' => 1,
            'nama' => 'John Doe',
            'nip' => '123456'
        ]);
        
        $this->assertInstanceOf(Pegawai::class, $pegawai);
        $this->assertEquals(1, $pegawai->pegawai_id);
        $this->assertEquals('John Doe', $pegawai->nama);
        $this->assertEquals('123456', $pegawai->nip);
    }

    /** @test */
    public function dev_type_model_can_be_instantiated()
    {
        $devType = new DevType([
            'id_type' => 1,
            'type_name' => 'Fingerprint Scanner',
            'description' => 'Biometric device'
        ]);
        
        $this->assertInstanceOf(DevType::class, $devType);
        $this->assertEquals(1, $devType->id_type);
        $this->assertEquals('Fingerprint Scanner', $devType->type_name);
        $this->assertEquals('Biometric device', $devType->description);
    }
}
