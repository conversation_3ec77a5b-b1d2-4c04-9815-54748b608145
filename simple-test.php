<?php

/**
 * Simple EasyLink Configuration Test
 * Tests configuration loading and basic functionality without Laravel dependencies
 */

require_once 'vendor/autoload.php';

use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Load environment variables from .env file
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: {$path}");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }
        
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $name = trim($name);
            $value = trim($value);
            
            if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
                putenv(sprintf('%s=%s', $name, $value));
                $_ENV[$name] = $value;
                $_SERVER[$name] = $value;
            }
        }
    }
}

// Helper function to get environment variable with default
function env($key, $default = null) {
    $value = getenv($key);
    if ($value === false) {
        return $default;
    }
    return $value;
}

echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║                EasyLink Configuration Test                   ║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n\n";

// Load .env file
try {
    loadEnv(__DIR__ . '/.env');
    echo "✅ Environment variables loaded from .env file\n\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Configuration from environment variables
$config = [
    'host' => env('EASYLINK_SDK_HOST', 'http://**************:5005'),
    'serial_number' => env('EASYLINK_SDK_SN', '1234567890'),
    'server_port' => env('EASYLINK_SERVER_PORT', '7005'),
    'db_host' => env('EASYLINK_DB_HOST', 'localhost'),
    'db_port' => env('EASYLINK_DB_PORT', '3306'),
    'db_database' => env('EASYLINK_DB_DATABASE', 'fingerspot'),
    'db_username' => env('EASYLINK_DB_USERNAME', 'root'),
    'db_password' => env('EASYLINK_DB_PASSWORD', ''),
];

echo "📋 Configuration Summary:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
foreach ($config as $key => $value) {
    $displayValue = ($key === 'db_password' && empty($value)) ? '(empty)' : $value;
    if ($key === 'db_password' && !empty($value)) {
        $displayValue = str_repeat('*', strlen($value));
    }
    echo "• " . str_pad(ucwords(str_replace('_', ' ', $key)) . ':', 20) . $displayValue . "\n";
}
echo "\n";

// Test 1: Network Connectivity
echo "🌐 Network Connectivity Test:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";

$host = '**************';
$devicePort = 5005;
$serverPort = 7005;

// Test device port
echo "Testing device port {$devicePort}...\n";
$deviceConnection = @fsockopen($host, $devicePort, $errno, $errstr, 5);
if ($deviceConnection) {
    echo "✅ Device port {$devicePort}: Accessible\n";
    fclose($deviceConnection);
    $deviceAccessible = true;
} else {
    echo "❌ Device port {$devicePort}: Not accessible ({$errstr})\n";
    $deviceAccessible = false;
}

// Test server port
echo "Testing server port {$serverPort}...\n";
$serverConnection = @fsockopen($host, $serverPort, $errno, $errstr, 5);
if ($serverConnection) {
    echo "✅ Server port {$serverPort}: Accessible\n";
    fclose($serverConnection);
} else {
    echo "⚠️  Server port {$serverPort}: Not accessible (normal if no server running)\n";
}

echo "\n";

// Test 2: Database Connectivity
echo "🗄️  Database Connectivity Test:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";

if (extension_loaded('pdo_mysql')) {
    try {
        $dsn = "mysql:host={$config['db_host']};port={$config['db_port']};dbname={$config['db_database']}";
        $pdo = new PDO($dsn, $config['db_username'], $config['db_password'], [
            PDO::ATTR_TIMEOUT => 5,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        echo "✅ Database connection successful!\n";
        echo "   Database: {$config['db_database']} on {$config['db_host']}:{$config['db_port']}\n";
        
        // Test if fingerspot tables exist
        $tables = ['device', 'pegawai', 'att_log', 'pegawai_d'];
        $foundTables = 0;
        foreach ($tables as $table) {
            $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                echo "   ✅ Table '{$table}': Found\n";
                $foundTables++;
            } else {
                echo "   ⚠️  Table '{$table}': Not found\n";
            }
        }
        
        if ($foundTables > 0) {
            echo "   📊 Found {$foundTables} out of " . count($tables) . " expected tables\n";
        } else {
            echo "   ⚠️  No fingerspot tables found - you may need to set up the database\n";
        }
        
    } catch (PDOException $e) {
        echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ PDO MySQL extension not loaded\n";
}

echo "\n";

// Test 3: EasylinkUser DTO Test
echo "👤 EasylinkUser DTO Test:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";

try {
    // Create user object
    $user = new EasylinkUser(
        sn: $config['serial_number'],
        pin: '001',
        nama: 'Test User',
        pwd: 'password123',
        rfid: 'RFID001',
        priv: 1,
        tmp: null
    );
    
    echo "✅ EasylinkUser object created successfully\n";
    echo "   PIN: {$user->pin}\n";
    echo "   Name: {$user->nama}\n";
    echo "   RFID: {$user->rfid}\n";
    echo "   Privilege: {$user->priv}\n";
    
    // Test array conversion
    $userArray = $user->toArray();
    echo "✅ User converted to array: " . count($userArray) . " fields\n";
    
    // Test from array creation
    $userData = [
        'sn' => $config['serial_number'],
        'pin' => '002',
        'nama' => 'Another User',
        'pwd' => 'test123'
    ];
    
    $user2 = EasylinkUser::fromArray($userData);
    echo "✅ User created from array: {$user2->nama}\n";
    echo "   Default values applied: pwd='{$user2->pwd}', rfid='{$user2->rfid}', priv={$user2->priv}\n";
    
} catch (Exception $e) {
    echo "❌ EasylinkUser test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Configuration Validation
echo "🔧 Configuration Validation:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";

$validationResults = [];

// Validate host URL
if (filter_var($config['host'], FILTER_VALIDATE_URL)) {
    echo "✅ Host URL format is valid\n";
    $validationResults['host'] = true;
} else {
    echo "❌ Host URL format is invalid\n";
    $validationResults['host'] = false;
}

// Validate serial number
if (!empty($config['serial_number']) && strlen($config['serial_number']) > 5) {
    echo "✅ Serial number looks valid\n";
    $validationResults['serial'] = true;
} else {
    echo "❌ Serial number appears to be invalid or too short\n";
    $validationResults['serial'] = false;
}

// Validate ports
if (is_numeric($config['server_port']) && $config['server_port'] > 0 && $config['server_port'] < 65536) {
    echo "✅ Server port is valid\n";
    $validationResults['port'] = true;
} else {
    echo "❌ Server port is invalid\n";
    $validationResults['port'] = false;
}

echo "\n";

// Summary
echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║                        Test Summary                          ║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n";

$allGood = true;
echo "📊 Results:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
echo ($deviceAccessible ? "✅" : "❌") . " Device connectivity\n";
echo "✅ Configuration loading\n";
echo "✅ DTO functionality\n";
echo ($validationResults['host'] ? "✅" : "❌") . " Host URL validation\n";
echo ($validationResults['serial'] ? "✅" : "❌") . " Serial number validation\n";

if ($deviceAccessible && $validationResults['host'] && $validationResults['serial']) {
    echo "\n🎉 Your configuration is ready for Laravel integration!\n";
    echo "\nNext steps:\n";
    echo "1. Create a new Laravel application\n";
    echo "2. Install this package: composer require kangangga/laravel-easylink\n";
    echo "3. Copy your .env settings to the Laravel app\n";
    echo "4. Use the EasylinkSdk facade in your controllers\n";
} else {
    echo "\n⚠️  Some issues found. Please check:\n";
    if (!$deviceAccessible) echo "   • Device connectivity\n";
    if (!$validationResults['host']) echo "   • Host URL format\n";
    if (!$validationResults['serial']) echo "   • Serial number\n";
}

echo "\n📚 Available documentation:\n";
echo "   • README.md - Basic usage\n";
echo "   • CONFIGURATION.md - Detailed configuration\n";
echo "   • SETUP-GUIDE.md - Step-by-step setup\n\n";
