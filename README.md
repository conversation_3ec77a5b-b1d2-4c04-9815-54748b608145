# Laravel EasyLink SDK

[![Latest Version on Packagist](https://img.shields.io/packagist/v/kangangga/laravel-easylink.svg?style=flat-square)](https://packagist.org/packages/kangangga/laravel-easylink)
[![Total Downloads](https://img.shields.io/packagist/dt/kangangga/laravel-easylink.svg?style=flat-square)](https://packagist.org/packages/kangangga/laravel-easylink)
![GitHub Actions](https://github.com/kangangga/laravel-easylink/actions/workflows/main.yml/badge.svg)

A Laravel package for communicating with Fingerspot EasyLink SDK devices. This package provides a simple and elegant way to integrate fingerprint attendance systems with your Laravel applications.

## Features

- 🔌 Easy integration with Fingerspot EasyLink devices
- 👥 User management (add, delete users)
- 📊 Attendance log retrieval (all logs, new logs only)
- 🖥️ Device information retrieval
- 🗄️ Eloquent models for database operations
- 🧪 Comprehensive test suite
- ⚙️ Configurable via environment variables

## Installation

You can install the package via composer:

```bash
composer require kangangga/laravel-easylink
```

## Configuration

### Environment Variables

Copy the `.env.example` file and configure your EasyLink device settings:

```env
# EasyLink Device Configuration
EASYLINK_SDK_HOST=http://*************:8080
EASYLINK_SDK_SN=Fio616231022480092

# Database Configuration
EASYLINK_DB_HOST=localhost
EASYLINK_DB_PORT=3306
EASYLINK_DB_DATABASE=fingerspot
EASYLINK_DB_USERNAME=your_username
EASYLINK_DB_PASSWORD=your_password
```

### Publish Configuration (Optional)

```bash
php artisan vendor:publish --provider="Kangangga\EasylinkSdk\EasylinkSdkServiceProvider" --tag="config"
```

## Usage

### Basic SDK Usage

```php
use Kangangga\EasylinkSdk\EasylinkSdk;
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Create SDK instance
$sdk = new EasylinkSdk('http://*************:8080', 'Fio616231022480092');

// Get device information
$deviceInfo = $sdk->device();

// Get attendance logs
$logs = $sdk->scanlogAll();
$newLogs = $sdk->scanlogNew();
```

### Using the Facade

```php
use EasylinkSdk;

// Get device information
$deviceInfo = EasylinkSdk::device();

// Get attendance logs
$logs = EasylinkSdk::scanlogAll();
```

### User Management

```php
// Create a new user
$user = new EasylinkUser(
    sn: 'Fio616231022480092',
    pin: '001',
    nama: 'John Doe',
    pwd: 'password123',
    rfid: 'RFID123',
    priv: 1,
    tmp: null
);

$response = $sdk->userSet($user);

// Delete users
$response = $sdk->userDel();
```

### Database Models

```php
use Kangangga\EasylinkSdk\Models\Device;
use Kangangga\EasylinkSdk\Models\Pegawai;
use Kangangga\EasylinkSdk\Models\AttLog;

// Get all devices
$devices = Device::all();

// Get employee data with details
$employees = Pegawai::with('detail')->get();

// Get today's attendance logs
$attendance = AttLog::whereDate('scan_date', today())->get();
```

### Testing

```bash
composer test
```

### Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information what has changed recently.

## Contributing

Please see [CONTRIBUTING](CONTRIBUTING.md) for details.

### Security

If you discover any security related issues, <NAME_EMAIL> instead of using the issue tracker.

## Credits

-   [Angga Saputra](https://github.com/kangangga)
-   [All Contributors](../../contributors)

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.

## Laravel Package Boilerplate

This package was generated using the [Laravel Package Boilerplate](https://laravelpackageboilerplate.com).
