<?php

/**
 * EasyLink SDK Usage Example
 * 
 * This example demonstrates how to use the Laravel EasyLink SDK
 * with your specific device configuration:
 * - IP: **************
 * - Device Port: 5005
 * - Server Port: 7005
 */

require_once 'vendor/autoload.php';

use Kangangga\EasylinkSdk\EasylinkSdk;
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Configuration
$deviceHost = 'http://**************:5005';
$serialNumber = '66208023321907'; // Replace with actual serial number

echo "=== EasyLink SDK Usage Example ===\n";
echo "Device: {$deviceHost}\n";
echo "Serial: {$serialNumber}\n";
echo "===================================\n\n";

try {
    // Initialize SDK
    $sdk = new EasylinkSdk($deviceHost, $serialNumber);
    echo "✓ SDK initialized successfully\n\n";

    // Example 1: Get Device Information
    echo "1. Getting device information...\n";
    $deviceInfo = $sdk->device();
    
    if ($deviceInfo->get('Result')) {
        echo "   ✓ Device info retrieved successfully\n";
        $data = $deviceInfo->get('Data', []);
        foreach ($data as $key => $value) {
            echo "   {$key}: {$value}\n";
        }
    } else {
        echo "   ✗ Failed to get device info: " . $deviceInfo->get('Message') . "\n";
    }
    echo "\n";

    // Example 2: Get New Scan Logs
    echo "2. Getting new scan logs...\n";
    $scanLogs = $sdk->scanlogNew();
    
    if ($scanLogs->get('Result')) {
        $logs = $scanLogs->get('Data', []);
        echo "   ✓ Found " . count($logs) . " new scan logs\n";
        
        if (count($logs) > 0) {
            echo "   Recent scan logs:\n";
            foreach (array_slice($logs, 0, 5) as $log) { // Show first 5 logs
                echo "     PIN: {$log['PIN']}, Date: {$log['date']}, NIP: {$log['nip']}\n";
            }
        }
    } else {
        echo "   ✗ Failed to get scan logs: " . $scanLogs->get('Message') . "\n";
    }
    echo "\n";

    // Example 3: Get All Scan Logs
    echo "3. Getting all scan logs...\n";
    $allLogs = $sdk->scanlogAll();
    
    if ($allLogs->get('Result')) {
        $logs = $allLogs->get('Data', []);
        echo "   ✓ Found " . count($logs) . " total scan logs\n";
        
        // Show statistics
        $pins = array_unique(array_column($logs, 'PIN'));
        echo "   Unique employees: " . count($pins) . "\n";
        
        if (count($logs) > 0) {
            $latestLog = $logs[0];
            echo "   Latest scan: PIN {$latestLog['PIN']} at {$latestLog['date']}\n";
        }
    } else {
        echo "   ✗ Failed to get all scan logs: " . $allLogs->get('Message') . "\n";
    }
    echo "\n";

    // Example 4: User Management (commented out to avoid accidental execution)
    echo "4. User management example (commented out for safety)...\n";
    /*
    // Create a new user
    $newUser = new EasylinkUser(
        sn: $serialNumber,
        pin: '999',
        nama: 'Test User',
        pwd: 'test123',
        rfid: 'TEST001',
        priv: 0,
        tmp: null
    );

    $userResult = $sdk->userSet($newUser);
    
    if ($userResult->get('Result')) {
        echo "   ✓ User created successfully\n";
    } else {
        echo "   ✗ Failed to create user: " . $userResult->get('Message') . "\n";
    }

    // Delete users (be careful with this!)
    // $deleteResult = $sdk->userDel();
    */
    echo "   (Uncomment the code above to test user management)\n\n";

} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
}

// Example 5: Laravel Integration Examples
echo "5. Laravel Integration Examples:\n";
echo "\n";

echo "   A. Using in a Controller:\n";
echo "   ```php\n";
echo "   use Kangangga\\EasylinkSdk\\EasylinkSdk;\n";
echo "   \n";
echo "   class AttendanceController extends Controller\n";
echo "   {\n";
echo "       public function syncAttendance()\n";
echo "       {\n";
echo "           \$sdk = new EasylinkSdk('{$deviceHost}', '{$serialNumber}');\n";
echo "           \$logs = \$sdk->scanlogNew();\n";
echo "           \n";
echo "           if (\$logs->get('Result')) {\n";
echo "               // Process attendance data\n";
echo "               foreach (\$logs->get('Data') as \$log) {\n";
echo "                   // Save to database\n";
echo "               }\n";
echo "           }\n";
echo "           \n";
echo "           return response()->json(\$logs->toArray());\n";
echo "       }\n";
echo "   }\n";
echo "   ```\n\n";

echo "   B. Using the Facade:\n";
echo "   ```php\n";
echo "   use EasylinkSdk;\n";
echo "   \n";
echo "   // In your controller or service\n";
echo "   \$deviceInfo = EasylinkSdk::device();\n";
echo "   \$attendanceLogs = EasylinkSdk::scanlogNew();\n";
echo "   ```\n\n";

echo "   C. Using with Eloquent Models:\n";
echo "   ```php\n";
echo "   use Kangangga\\EasylinkSdk\\Models\\AttLog;\n";
echo "   use Kangangga\\EasylinkSdk\\Models\\Pegawai;\n";
echo "   \n";
echo "   // Get attendance logs from database\n";
echo "   \$todayLogs = AttLog::whereDate('scan_date', today())->get();\n";
echo "   \n";
echo "   // Get employee data\n";
echo "   \$employees = Pegawai::with('detail')->get();\n";
echo "   ```\n\n";

echo "=== Example Complete ===\n";
echo "\nNext Steps:\n";
echo "1. Replace 'your_device_serial_number' with your actual device serial number\n";
echo "2. Ensure your device is accessible at **************:5005\n";
echo "3. Test the connection using: php test-connection.php\n";
echo "4. Integrate the SDK into your Laravel application\n";
echo "5. Set up proper error handling and logging\n\n";
