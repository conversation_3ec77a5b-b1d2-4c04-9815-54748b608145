<?php

namespace Kangangga\EasylinkSdk\Tests;

use Kangangga\EasylinkSdk\EasylinkSdk;
use Kangangga\EasylinkSdk\EasylinkSdkServiceProvider;
use Kangangga\EasylinkSdk\EasylinkSdkFacade;

class ServiceProviderTest extends TestCase
{
    /** @test */
    public function it_registers_the_service_provider()
    {
        $provider = $this->app->resolveProvider(EasylinkSdkServiceProvider::class);
        $this->assertInstanceOf(EasylinkSdkServiceProvider::class, $provider);
    }

    /** @test */
    public function it_binds_easylink_sdk_to_container()
    {
        $this->app['config']->set('laravel-easylink.sdk.host', 'http://localhost:8080');
        $this->app['config']->set('laravel-easylink.sdk.serial_number', 'TEST123456');

        $sdk = $this->app->make('laravel-easylink');
        
        $this->assertInstanceOf(EasylinkSdk::class, $sdk);
        $this->assertEquals('http://localhost:8080', $sdk->getHost());
        $this->assertEquals('TEST123456', $sdk->getSerialNumber());
    }

    /** @test */
    public function it_merges_config_from_package()
    {
        $this->assertNotNull(config('laravel-easylink'));
        $this->assertArrayHasKey('sdk', config('laravel-easylink'));
        $this->assertArrayHasKey('host', config('laravel-easylink.sdk'));
        $this->assertArrayHasKey('serial_number', config('laravel-easylink.sdk'));
    }

    /** @test */
    public function it_sets_database_connection_config()
    {
        $this->app['config']->set('laravel-easylink.database', [
            'driver' => 'mysql',
            'host' => 'localhost',
            'database' => 'easylink_test',
            'username' => 'root',
            'password' => 'password'
        ]);

        // Re-register the service provider to trigger the config merge
        $provider = new EasylinkSdkServiceProvider($this->app);
        $provider->register();

        $this->assertEquals('mysql', config('database.connections.easylink.driver'));
        $this->assertEquals('localhost', config('database.connections.easylink.host'));
        $this->assertEquals('easylink_test', config('database.connections.easylink.database'));
    }

    /** @test */
    public function it_can_resolve_via_facade()
    {
        $this->app['config']->set('laravel-easylink.sdk.host', 'http://localhost:8080');
        $this->app['config']->set('laravel-easylink.sdk.serial_number', 'TEST123456');

        $sdk = EasylinkSdkFacade::getFacadeRoot();
        
        $this->assertInstanceOf(EasylinkSdk::class, $sdk);
    }

    /** @test */
    public function it_uses_default_config_values()
    {
        // Test that the config has the expected default values from the config file
        $this->assertNotNull(config('laravel-easylink.sdk.host'));
        $this->assertNotNull(config('laravel-easylink.sdk.serial_number'));

        // The defaults should be set from the config file
        $this->assertIsString(config('laravel-easylink.sdk.host'));
        $this->assertIsString(config('laravel-easylink.sdk.serial_number'));
    }

    /** @test */
    public function it_can_publish_config_file()
    {
        $provider = new EasylinkSdkServiceProvider($this->app);
        
        // Simulate console environment
        $this->app->instance('path.config', '/fake/config/path');
        
        // Get publishable assets
        $publishable = $provider::$publishes;
        
        $this->assertNotEmpty($publishable);
        
        // Check if config publishing is registered
        $configPublishes = $provider::$publishes[EasylinkSdkServiceProvider::class] ?? [];
        $this->assertNotEmpty($configPublishes);
    }

    /** @test */
    public function it_handles_environment_variables()
    {
        // Test that environment variables can be used for configuration
        // We'll test this by checking that the config uses env() function
        $this->assertTrue(function_exists('env'));

        // Test that the config structure supports environment variables
        $this->assertArrayHasKey('sdk', config('laravel-easylink'));
        $this->assertArrayHasKey('host', config('laravel-easylink.sdk'));
        $this->assertArrayHasKey('serial_number', config('laravel-easylink.sdk'));
    }

    /** @test */
    public function it_creates_new_instance_each_time()
    {
        $this->app['config']->set('laravel-easylink.sdk.host', 'http://localhost:8080');
        $this->app['config']->set('laravel-easylink.sdk.serial_number', 'TEST123456');

        $sdk1 = $this->app->make('laravel-easylink');
        $sdk2 = $this->app->make('laravel-easylink');
        
        // Should be different instances but same class
        $this->assertInstanceOf(EasylinkSdk::class, $sdk1);
        $this->assertInstanceOf(EasylinkSdk::class, $sdk2);
        $this->assertNotSame($sdk1, $sdk2);
    }

    /** @test */
    public function it_handles_missing_config_gracefully()
    {
        // Test that the service provider can handle missing config values
        $this->app['config']->set('laravel-easylink.sdk.host', null);
        $this->app['config']->set('laravel-easylink.sdk.serial_number', null);

        $sdk = $this->app->make('laravel-easylink');

        $this->assertInstanceOf(EasylinkSdk::class, $sdk);
        // Should handle null values gracefully
        $this->assertNull($sdk->getHost());
        $this->assertNull($sdk->getSerialNumber());
    }
}
