<?php

/**
 * Laravel EasyLink SDK Demo Application
 * 
 * This script demonstrates how to use the EasyLink SDK
 * with your device configuration: IP **************:5005
 */

require_once 'vendor/autoload.php';

use Kangangga\EasylinkSdk\EasylinkSdk;
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Load environment variables from .env file
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: {$path}");
    }

    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }

        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $name = trim($name);
            $value = trim($value);

            if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
                putenv(sprintf('%s=%s', $name, $value));
                $_ENV[$name] = $value;
                $_SERVER[$name] = $value;
            }
        }
    }
}

// Helper function to get environment variable with default
function env($key, $default = null) {
    $value = getenv($key);
    if ($value === false) {
        return $default;
    }
    return $value;
}

// Load .env file
try {
    loadEnv(__DIR__ . '/.env');
    echo "✅ Environment variables loaded from .env file\n\n";
} catch (Exception $e) {
    echo "⚠️  Warning: " . $e->getMessage() . "\n";
    echo "Using default configuration...\n\n";
}

// Configuration from environment variables
$config = [
    'host' => env('EASYLINK_SDK_HOST', 'http://**************:5005'),
    'serial_number' => env('EASYLINK_SDK_SN', '1234567890'),
    'server_port' => env('EASYLINK_SERVER_PORT', '7005'),
    'db_host' => env('EASYLINK_DB_HOST', 'localhost'),
    'db_port' => env('EASYLINK_DB_PORT', '3306'),
    'db_database' => env('EASYLINK_DB_DATABASE', 'fingerspot'),
    'db_username' => env('EASYLINK_DB_USERNAME', 'root'),
    'db_password' => env('EASYLINK_DB_PASSWORD', ''),
];

echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║                  EasyLink SDK Demo Application               ║\n";
echo "╠══════════════════════════════════════════════════════════════╣\n";
echo "║ Configuration loaded from .env file:                        ║\n";
echo "║                                                              ║\n";
echo "║ Device Host: " . str_pad($config['host'], 41) . "║\n";
echo "║ Serial Number: " . str_pad($config['serial_number'], 39) . "║\n";
echo "║ Server Port: " . str_pad($config['server_port'], 41) . "║\n";
echo "║ Database: " . str_pad($config['db_database'], 44) . "║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n\n";

try {
    // Initialize SDK
    echo "🔧 Initializing EasyLink SDK...\n";
    $sdk = new EasylinkSdk($config['host'], $config['serial_number']);
    echo "✅ SDK initialized successfully!\n\n";

    // Test 1: Device Information
    echo "📱 Testing Device Information...\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    try {
        $deviceInfo = $sdk->device();
        
        if ($deviceInfo && $deviceInfo->get('Result')) {
            echo "✅ Device connection successful!\n";
            echo "📊 Device Information:\n";
            
            $data = $deviceInfo->get('Data', []);
            if (is_array($data) && count($data) > 0) {
                foreach ($data as $key => $value) {
                    echo "   • {$key}: {$value}\n";
                }
            } else {
                echo "   • Device responded but no detailed info available\n";
            }
        } else {
            $message = $deviceInfo ? $deviceInfo->get('Message', 'Unknown error') : 'No response';
            echo "❌ Device connection failed: {$message}\n";
        }
    } catch (Exception $e) {
        echo "❌ Device connection error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";

    // Test 2: Attendance Logs
    echo "📋 Testing Attendance Logs...\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    try {
        $scanLogs = $sdk->scanlogNew();
        
        if ($scanLogs && $scanLogs->get('Result')) {
            $logs = $scanLogs->get('Data', []);
            echo "✅ Attendance logs retrieved successfully!\n";
            echo "📈 Found " . count($logs) . " new attendance records\n";
            
            if (count($logs) > 0) {
                echo "\n🕐 Recent Attendance Records:\n";
                foreach (array_slice($logs, 0, 5) as $index => $log) {
                    $pin = $log['PIN'] ?? 'N/A';
                    $date = $log['date'] ?? $log['ScanDate'] ?? 'N/A';
                    $nip = $log['nip'] ?? $pin;
                    echo "   " . ($index + 1) . ". Employee PIN: {$pin} | NIP: {$nip} | Time: {$date}\n";
                }
                
                if (count($logs) > 5) {
                    echo "   ... and " . (count($logs) - 5) . " more records\n";
                }
            } else {
                echo "ℹ️  No new attendance records found\n";
            }
        } else {
            $message = $scanLogs ? $scanLogs->get('Message', 'Unknown error') : 'No response';
            echo "❌ Failed to retrieve attendance logs: {$message}\n";
        }
    } catch (Exception $e) {
        echo "❌ Attendance logs error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";

    // Test 3: User Management Demo (Safe - just shows how to create user object)
    echo "👤 User Management Demo...\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    // Create a sample user object (not actually sent to device)
    $sampleUser = new EasylinkUser(
        sn: $config['serial_number'],
        pin: '999',
        nama: 'Demo User',
        pwd: 'demo123',
        rfid: 'DEMO001',
        priv: 0,
        tmp: null
    );
    
    echo "✅ Sample user object created:\n";
    echo "   • PIN: {$sampleUser->pin}\n";
    echo "   • Name: {$sampleUser->nama}\n";
    echo "   • RFID: {$sampleUser->rfid}\n";
    echo "   • Privilege: {$sampleUser->priv}\n";
    
    // Convert to array
    $userArray = $sampleUser->toArray();
    echo "   • Array conversion: " . count($userArray) . " fields\n";
    
    echo "\nℹ️  Note: User not actually added to device (demo mode)\n";
    echo "   To add users, uncomment the userSet() call in the code\n";
    
    echo "\n";

    // Test 4: Database Connectivity Test
    echo "🗄️  Database Connectivity Test...\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";

    try {
        $dbHost = $config['db_host'];
        $dbPort = $config['db_port'];
        $dbName = $config['db_database'];
        $dbUser = $config['db_username'];
        $dbPass = $config['db_password'];

        echo "📊 Database Configuration:\n";
        echo "   • Host: {$dbHost}:{$dbPort}\n";
        echo "   • Database: {$dbName}\n";
        echo "   • Username: {$dbUser}\n";
        echo "   • Password: " . (empty($dbPass) ? "(empty)" : "***") . "\n";

        // Test database connection
        if (extension_loaded('pdo_mysql')) {
            try {
                $dsn = "mysql:host={$dbHost};port={$dbPort};dbname={$dbName}";
                $pdo = new PDO($dsn, $dbUser, $dbPass, [
                    PDO::ATTR_TIMEOUT => 5,
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                ]);
                echo "✅ Database connection successful!\n";

                // Test if fingerspot tables exist
                $tables = ['device', 'pegawai', 'att_log'];
                foreach ($tables as $table) {
                    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                    $stmt->execute([$table]);
                    if ($stmt->rowCount() > 0) {
                        echo "   • Table '{$table}': ✅ Found\n";
                    } else {
                        echo "   • Table '{$table}': ⚠️  Not found\n";
                    }
                }

            } catch (PDOException $e) {
                echo "❌ Database connection failed: " . $e->getMessage() . "\n";
            }
        } else {
            echo "⚠️  PDO MySQL extension not loaded - cannot test database connection\n";
        }
    } catch (Exception $e) {
        echo "❌ Database test error: " . $e->getMessage() . "\n";
    }

    echo "\n";

    // Test 5: Network Connectivity Summary
    echo "🌐 Network Connectivity Summary...\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $host = '**************';
    $devicePort = 5005;
    $serverPort = 7005;
    
    // Test device port
    $deviceConnection = @fsockopen($host, $devicePort, $errno, $errstr, 3);
    if ($deviceConnection) {
        echo "✅ Device port {$devicePort}: Accessible\n";
        fclose($deviceConnection);
    } else {
        echo "❌ Device port {$devicePort}: Not accessible ({$errstr})\n";
    }
    
    // Test server port
    $serverConnection = @fsockopen($host, $serverPort, $errno, $errstr, 3);
    if ($serverConnection) {
        echo "✅ Server port {$serverPort}: Accessible\n";
        fclose($serverConnection);
    } else {
        echo "⚠️  Server port {$serverPort}: Not accessible (normal if no server running)\n";
    }

} catch (Exception $e) {
    echo "💥 Critical Error: " . $e->getMessage() . "\n";
    echo "📁 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n";
echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║                        Demo Complete!                        ║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n";

echo "\n🚀 Next Steps:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
echo "1. 🔧 Update the serial number in this script with your actual device serial\n";
echo "2. 🏗️  Integrate this package into your Laravel application:\n";
echo "   • Add to composer.json: \"kangangga/laravel-easylink\": \"*\"\n";
echo "   • Run: composer install\n";
echo "   • Configure .env with your device settings\n";
echo "3. 📝 Use in your Laravel controllers:\n";
echo "   • use EasylinkSdk;\n";
echo "   • \$logs = EasylinkSdk::scanlogNew();\n";
echo "4. 🗄️  Set up database connection for attendance data storage\n";
echo "5. 🔒 Implement proper error handling and logging\n\n";

echo "📚 Documentation:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
echo "• README.md - Basic usage and installation\n";
echo "• CONFIGURATION.md - Detailed configuration guide\n";
echo "• SETUP-GUIDE.md - Step-by-step setup for your device\n";
echo "• example-usage.php - More usage examples\n\n";

echo "🧪 Testing:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
echo "• Run: composer test (run all package tests)\n";
echo "• Run: php test-connection.php (test device connectivity)\n";
echo "• Run: php run-demo.php (this demo script)\n\n";
