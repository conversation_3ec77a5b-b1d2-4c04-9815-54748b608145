<?php

/**
 * Standalone EasyLink SDK Demo
 * Reads configuration from .env file and tests the device
 */

require_once 'vendor/autoload.php';

use Kangangga\EasylinkSdk\EasylinkSdk;
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Load environment variables from .env file
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: {$path}");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue; // Skip comments
        }
        
        if (strpos($line, '=') !== false) {
            list($name, $value) = explode('=', $line, 2);
            $name = trim($name);
            $value = trim($value);
            
            if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
                putenv(sprintf('%s=%s', $name, $value));
                $_ENV[$name] = $value;
                $_SERVER[$name] = $value;
            }
        }
    }
}

// Helper function to get environment variable with default
function env($key, $default = null) {
    $value = getenv($key);
    if ($value === false) {
        return $default;
    }
    return $value;
}

// Load .env file
try {
    loadEnv(__DIR__ . '/.env');
    echo "✅ Environment variables loaded from .env file\n\n";
} catch (Exception $e) {
    echo "⚠️  Warning: " . $e->getMessage() . "\n";
    echo "Using default configuration...\n\n";
}

// Configuration from environment variables
$config = [
    'host' => env('EASYLINK_SDK_HOST', 'http://**************:5005'),
    'serial_number' => env('EASYLINK_SDK_SN', '1234567890'),
    'server_port' => env('EASYLINK_SERVER_PORT', '7005'),
    'db_host' => env('EASYLINK_DB_HOST', 'localhost'),
    'db_port' => env('EASYLINK_DB_PORT', '3306'),
    'db_database' => env('EASYLINK_DB_DATABASE', 'fingerspot'),
    'db_username' => env('EASYLINK_DB_USERNAME', 'root'),
    'db_password' => env('EASYLINK_DB_PASSWORD', ''),
];

echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║              Standalone EasyLink SDK Demo                    ║\n";
echo "╠══════════════════════════════════════════════════════════════╣\n";
echo "║ Configuration from .env:                                    ║\n";
echo "║                                                              ║\n";
echo "║ Device Host: " . str_pad($config['host'], 41) . "║\n";
echo "║ Serial Number: " . str_pad($config['serial_number'], 39) . "║\n";
echo "║ Server Port: " . str_pad($config['server_port'], 41) . "║\n";
echo "║ Database: " . str_pad($config['db_database'], 44) . "║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n\n";

try {
    // Initialize SDK directly (no Laravel facades)
    echo "🔧 Initializing EasyLink SDK...\n";
    $sdk = new EasylinkSdk($config['host'], $config['serial_number']);
    echo "✅ SDK initialized successfully!\n";
    echo "   Host: " . $sdk->getHost() . "\n";
    echo "   Serial: " . $sdk->getSerialNumber() . "\n\n";

    // Test 1: Network Connectivity
    echo "🌐 Testing Network Connectivity...\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $host = '**************';
    $devicePort = 5005;
    $serverPort = 7005;
    
    // Test device port
    $deviceConnection = @fsockopen($host, $devicePort, $errno, $errstr, 3);
    if ($deviceConnection) {
        echo "✅ Device port {$devicePort}: Accessible\n";
        fclose($deviceConnection);
        $deviceAccessible = true;
    } else {
        echo "❌ Device port {$devicePort}: Not accessible ({$errstr})\n";
        $deviceAccessible = false;
    }
    
    // Test server port
    $serverConnection = @fsockopen($host, $serverPort, $errno, $errstr, 3);
    if ($serverConnection) {
        echo "✅ Server port {$serverPort}: Accessible\n";
        fclose($serverConnection);
    } else {
        echo "⚠️  Server port {$serverPort}: Not accessible (normal if no server running)\n";
    }
    
    echo "\n";

    // Test 2: Device API calls (only if device is accessible)
    if ($deviceAccessible) {
        echo "📱 Testing Device API Calls...\n";
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
        
        try {
            // Test device info
            echo "   📊 Getting device information...\n";
            $deviceInfo = $sdk->device();
            
            if ($deviceInfo && is_object($deviceInfo) && method_exists($deviceInfo, 'get')) {
                if ($deviceInfo->get('Result')) {
                    echo "   ✅ Device info retrieved successfully!\n";
                    $data = $deviceInfo->get('Data', []);
                    if (is_array($data) && count($data) > 0) {
                        foreach ($data as $key => $value) {
                            echo "      • {$key}: {$value}\n";
                        }
                    } else {
                        echo "      • Device responded but no detailed info available\n";
                    }
                } else {
                    echo "   ⚠️  Device responded but returned error: " . $deviceInfo->get('Message', 'Unknown error') . "\n";
                }
            } else {
                echo "   ⚠️  Device info call completed but response format unexpected\n";
            }
            
            echo "\n   📋 Getting attendance logs...\n";
            $scanLogs = $sdk->scanlogNew();
            
            if ($scanLogs && is_object($scanLogs) && method_exists($scanLogs, 'get')) {
                if ($scanLogs->get('Result')) {
                    $logs = $scanLogs->get('Data', []);
                    echo "   ✅ Attendance logs retrieved: " . count($logs) . " entries\n";
                    
                    if (count($logs) > 0) {
                        echo "   📈 Recent attendance records:\n";
                        foreach (array_slice($logs, 0, 5) as $index => $log) {
                            $pin = $log['PIN'] ?? 'N/A';
                            $date = $log['date'] ?? $log['ScanDate'] ?? 'N/A';
                            $nip = $log['nip'] ?? $pin;
                            echo "      " . ($index + 1) . ". PIN: {$pin} | NIP: {$nip} | Time: {$date}\n";
                        }
                        
                        if (count($logs) > 5) {
                            echo "      ... and " . (count($logs) - 5) . " more records\n";
                        }
                    } else {
                        echo "   ℹ️  No new attendance records found\n";
                    }
                } else {
                    echo "   ⚠️  Scan logs responded but returned error: " . $scanLogs->get('Message', 'Unknown error') . "\n";
                }
            } else {
                echo "   ⚠️  Scan logs call completed but response format unexpected\n";
            }
            
        } catch (Exception $apiException) {
            echo "   ❌ API call failed: " . $apiException->getMessage() . "\n";
            echo "   This might be due to:\n";
            echo "      • Incorrect serial number\n";
            echo "      • Device authentication settings\n";
            echo "      • Device API version compatibility\n";
        }
    } else {
        echo "⚠️  Skipping API tests - device not accessible\n";
    }
    
    echo "\n";

    // Test 3: User DTO Demo
    echo "👤 User Management Demo...\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    $sampleUser = new EasylinkUser(
        sn: $config['serial_number'],
        pin: '999',
        nama: 'Demo User',
        pwd: 'demo123',
        rfid: 'DEMO001',
        priv: 0,
        tmp: null
    );
    
    echo "✅ Sample user object created:\n";
    echo "   • PIN: {$sampleUser->pin}\n";
    echo "   • Name: {$sampleUser->nama}\n";
    echo "   • RFID: {$sampleUser->rfid}\n";
    echo "   • Privilege: {$sampleUser->priv}\n";
    
    $userArray = $sampleUser->toArray();
    echo "   • Array conversion: " . count($userArray) . " fields\n";
    
    echo "\nℹ️  Note: User not actually added to device (demo mode)\n";
    
    echo "\n";

    // Test 4: Database Test
    echo "🗄️  Database Connectivity Test...\n";
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
    
    if (extension_loaded('pdo_mysql')) {
        try {
            $dsn = "mysql:host={$config['db_host']};port={$config['db_port']};dbname={$config['db_database']}";
            $pdo = new PDO($dsn, $config['db_username'], $config['db_password'], [
                PDO::ATTR_TIMEOUT => 5,
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            echo "✅ Database connection successful!\n";
            echo "   • Host: {$config['db_host']}:{$config['db_port']}\n";
            echo "   • Database: {$config['db_database']}\n";
            
            // Test if fingerspot tables exist
            $tables = ['device', 'pegawai', 'att_log'];
            foreach ($tables as $table) {
                $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                if ($stmt->rowCount() > 0) {
                    echo "   • Table '{$table}': ✅ Found\n";
                } else {
                    echo "   • Table '{$table}': ⚠️  Not found\n";
                }
            }
            
        } catch (PDOException $e) {
            echo "❌ Database connection failed: " . $e->getMessage() . "\n";
        }
    } else {
        echo "⚠️  PDO MySQL extension not loaded\n";
    }

} catch (Exception $e) {
    echo "💥 Critical Error: " . $e->getMessage() . "\n";
    echo "📁 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n";
echo "╔══════════════════════════════════════════════════════════════╗\n";
echo "║                     Demo Complete!                           ║\n";
echo "╚══════════════════════════════════════════════════════════════╝\n";

echo "\n🎯 Configuration Summary:\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
echo "✅ Environment variables loaded from .env\n";
echo "✅ Device configuration: {$config['host']}\n";
echo "✅ Serial number: {$config['serial_number']}\n";
echo "✅ Database: {$config['db_database']} on {$config['db_host']}\n\n";

echo "🚀 Ready for Laravel Integration!\n";
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
echo "Your .env file is properly configured. You can now:\n";
echo "1. Install this package in a Laravel app\n";
echo "2. Copy your .env settings to the Laravel app\n";
echo "3. Use EasylinkSdk facade in your controllers\n\n";
