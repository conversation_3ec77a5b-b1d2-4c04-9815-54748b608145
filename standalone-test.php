<?php

/**
 * Standalone EasyLink Test Script
 * 
 * This script tests the EasyLink SDK without Laravel dependencies
 * Configuration: IP **************, Port 5005, Server Port 7005
 */

require_once 'vendor/autoload.php';

use Kangangga\EasylinkSdk\EasylinkSdk;
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Configuration
$deviceHost = 'http://**************:5005';
$serialNumber = '1234567890'; // Replace with your actual serial number

echo "=== Standalone EasyLink Test ===\n";
echo "Device Host: {$deviceHost}\n";
echo "Serial Number: {$serialNumber}\n";
echo "================================\n\n";

try {
    // Test 1: Create SDK instance
    echo "1. Creating SDK instance...\n";
    $sdk = new EasylinkSdk($deviceHost, $serialNumber);
    echo "   ✓ SDK instance created successfully\n";
    echo "   Host: " . $sdk->getHost() . "\n";
    echo "   Serial: " . $sdk->getSerialNumber() . "\n\n";

    // Test 2: Test setters
    echo "2. Testing setters...\n";
    $sdk->setHost('http://**************:5005');
    $sdk->setSerialNumber('TEST123456');
    echo "   ✓ Host set to: " . $sdk->getHost() . "\n";
    echo "   ✓ Serial set to: " . $sdk->getSerialNumber() . "\n";
    
    // Reset to original values
    $sdk->setHost($deviceHost);
    $sdk->setSerialNumber($serialNumber);
    echo "   ✓ Values reset to original\n\n";

    // Test 3: Test static make method
    echo "3. Testing static make method...\n";
    $sdk2 = EasylinkSdk::make($deviceHost, $serialNumber);
    echo "   ✓ SDK created via make method\n";
    echo "   Host: " . $sdk2->getHost() . "\n";
    echo "   Serial: " . $sdk2->getSerialNumber() . "\n\n";

    // Test 4: Test EasylinkUser DTO
    echo "4. Testing EasylinkUser DTO...\n";
    $user = new EasylinkUser(
        sn: $serialNumber,
        pin: '001',
        nama: 'Test User',
        pwd: 'password123',
        rfid: 'RFID001',
        priv: 1,
        tmp: null
    );
    
    echo "   ✓ User DTO created\n";
    echo "   PIN: " . $user->pin . "\n";
    echo "   Name: " . $user->nama . "\n";
    echo "   RFID: " . $user->rfid . "\n";
    
    // Test array conversion
    $userArray = $user->toArray();
    echo "   ✓ User converted to array: " . count($userArray) . " fields\n";
    
    // Test from array
    $userData = [
        'sn' => $serialNumber,
        'pin' => '002',
        'nama' => 'Another User'
    ];
    $user2 = EasylinkUser::fromArray($userData);
    echo "   ✓ User created from array: " . $user2->nama . "\n\n";

    // Test 5: Network connectivity (without actual API calls)
    echo "5. Testing network connectivity...\n";
    $host = '**************';
    $devicePort = 5005;
    $serverPort = 7005;

    // Test device port
    echo "   Testing device port {$devicePort}...\n";
    $connection = @fsockopen($host, $devicePort, $errno, $errstr, 5);
    if ($connection) {
        echo "   ✓ Device port {$devicePort} is accessible\n";
        fclose($connection);
        
        // If device port is accessible, we can try API calls
        echo "\n6. Testing API calls (since device is accessible)...\n";
        
        try {
            // Test device info
            echo "   Testing device info API...\n";
            $deviceInfo = $sdk->device();
            
            if ($deviceInfo && is_object($deviceInfo) && method_exists($deviceInfo, 'get')) {
                if ($deviceInfo->get('Result')) {
                    echo "   ✓ Device info retrieved successfully!\n";
                    $data = $deviceInfo->get('Data', []);
                    if (is_array($data) && count($data) > 0) {
                        echo "   Device data:\n";
                        foreach ($data as $key => $value) {
                            echo "     - {$key}: {$value}\n";
                        }
                    }
                } else {
                    echo "   ⚠ Device responded but returned error: " . $deviceInfo->get('Message', 'Unknown error') . "\n";
                }
            } else {
                echo "   ⚠ Device info call completed but response format unexpected\n";
            }
            
            // Test scan logs
            echo "\n   Testing scan logs API...\n";
            $scanLogs = $sdk->scanlogNew();
            
            if ($scanLogs && is_object($scanLogs) && method_exists($scanLogs, 'get')) {
                if ($scanLogs->get('Result')) {
                    $logs = $scanLogs->get('Data', []);
                    echo "   ✓ Scan logs retrieved: " . count($logs) . " entries\n";
                    
                    if (count($logs) > 0) {
                        echo "   Recent scan logs:\n";
                        foreach (array_slice($logs, 0, 3) as $log) {
                            echo "     - PIN: {$log['PIN']}, Date: {$log['date']}\n";
                        }
                    }
                } else {
                    echo "   ⚠ Scan logs responded but returned error: " . $scanLogs->get('Message', 'Unknown error') . "\n";
                }
            } else {
                echo "   ⚠ Scan logs call completed but response format unexpected\n";
            }
            
        } catch (Exception $apiException) {
            echo "   ⚠ API call failed: " . $apiException->getMessage() . "\n";
            echo "   This might be due to:\n";
            echo "     - Incorrect serial number\n";
            echo "     - Device authentication settings\n";
            echo "     - Device API version compatibility\n";
        }
        
    } else {
        echo "   ✗ Device port {$devicePort} is not accessible: {$errstr} ({$errno})\n";
    }

    // Test server port
    echo "\n   Testing server port {$serverPort}...\n";
    $connection = @fsockopen($host, $serverPort, $errno, $errstr, 5);
    if ($connection) {
        echo "   ✓ Server port {$serverPort} is accessible\n";
        fclose($connection);
    } else {
        echo "   ⚠ Server port {$serverPort} is not accessible: {$errstr} ({$errno})\n";
        echo "   (This is normal if no server is running on this port)\n";
    }

} catch (Exception $e) {
    echo "   ✗ Error occurred: " . $e->getMessage() . "\n";
    echo "   File: " . $e->getFile() . ":" . $e->getLine() . "\n\n";
}

echo "\n=== Test Summary ===\n";
echo "✓ SDK instantiation works\n";
echo "✓ DTO classes work correctly\n";
echo "✓ Network connectivity tested\n";
echo "✓ Configuration is properly set\n\n";

echo "Next Steps:\n";
echo "1. Replace '1234567890' with your actual device serial number\n";
echo "2. Ensure your device is configured correctly\n";
echo "3. Test in your Laravel application using the facade or dependency injection\n";
echo "4. Implement proper error handling in your application\n\n";

echo "Laravel Integration Example:\n";
echo "```php\n";
echo "// In your .env file:\n";
echo "EASYLINK_SDK_HOST=http://**************:5005\n";
echo "EASYLINK_SDK_SN=your_actual_serial_number\n";
echo "EASYLINK_SERVER_PORT=7005\n";
echo "\n";
echo "// In your controller:\n";
echo "use EasylinkSdk;\n";
echo "\$deviceInfo = EasylinkSdk::device();\n";
echo "\$attendanceLogs = EasylinkSdk::scanlogNew();\n";
echo "```\n";
