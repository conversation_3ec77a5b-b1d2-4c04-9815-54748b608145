<?php

namespace Kangangga\EasylinkSdk\Tests;


use Kangangga\EasylinkSdk\EasylinkSdk;
use Kangangga\EasylinkSdk\EasylinkSdkFacade;
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

class UnitTest extends TestCase
{

    /** @test */
    public function it_check_string()
    {
        $val = intval('123123A');
        $this->assertEquals(123123, $val);
    }

    /** @test */
    public function it_check_provider()
    {
        $provider = $this->app->resolveProvider(\Kangangga\EasylinkSdk\EasylinkSdkServiceProvider::class);
        $this->assertInstanceOf(\Kangangga\EasylinkSdk\EasylinkSdkServiceProvider::class, $provider);
    }

    /** @test */
    public function it_can_create_easylink_sdk_instance()
    {
        $sdk = new EasylinkSdk('http://localhost:8080', 'TEST123456');

        $this->assertInstanceOf(EasylinkSdk::class, $sdk);
        $this->assertEquals('http://localhost:8080', $sdk->getHost());
        $this->assertEquals('TEST123456', $sdk->getSerialNumber());
    }

    /** @test */
    public function it_can_create_easylink_sdk_using_make_method()
    {
        $sdk = EasylinkSdk::make('http://localhost:8080', 'TEST123456');

        $this->assertInstanceOf(EasylinkSdk::class, $sdk);
        $this->assertEquals('http://localhost:8080', $sdk->getHost());
        $this->assertEquals('TEST123456', $sdk->getSerialNumber());
    }

    /** @test */
    public function it_can_set_host_and_serial_number()
    {
        $sdk = new EasylinkSdk('http://localhost:8080', 'TEST123456');

        $sdk->setHost('http://*************:8080');
        $sdk->setSerialNumber('NEW123456');

        $this->assertEquals('http://*************:8080', $sdk->getHost());
        $this->assertEquals('NEW123456', $sdk->getSerialNumber());
    }

    /** @test */
    public function it_can_access_sdk_via_facade()
    {
        $this->app['config']->set('laravel-easylink.sdk.host', 'http://localhost:8080');
        $this->app['config']->set('laravel-easylink.sdk.serial_number', 'TEST123456');

        $sdk = EasylinkSdkFacade::getFacadeRoot();
        $this->assertInstanceOf(EasylinkSdk::class, $sdk);
    }

    /** @test */
    public function it_can_create_easylink_user_dto()
    {
        $user = new EasylinkUser(
            sn: 'TEST123456',
            pin: '001',
            nama: 'John Doe',
            pwd: 'password123',
            rfid: 'RFID123',
            priv: 1,
            tmp: 'template_data'
        );

        $this->assertEquals('TEST123456', $user->sn);
        $this->assertEquals('001', $user->pin);
        $this->assertEquals('John Doe', $user->nama);
        $this->assertEquals('password123', $user->pwd);
        $this->assertEquals('RFID123', $user->rfid);
        $this->assertEquals(1, $user->priv);
        $this->assertEquals('template_data', $user->tmp);
    }

    /** @test */
    public function it_can_create_easylink_user_from_array()
    {
        $data = [
            'sn' => 'TEST123456',
            'pin' => '001',
            'nama' => 'John Doe',
            'pwd' => 'password123',
            'rfid' => 'RFID123',
            'priv' => 1,
            'tmp' => 'template_data'
        ];

        $user = EasylinkUser::fromArray($data);

        $this->assertEquals('TEST123456', $user->sn);
        $this->assertEquals('001', $user->pin);
        $this->assertEquals('John Doe', $user->nama);
    }

    /** @test */
    public function it_can_convert_easylink_user_to_array()
    {
        $user = new EasylinkUser(
            sn: 'TEST123456',
            pin: '001',
            nama: 'John Doe',
            pwd: 'password123',
            rfid: 'RFID123',
            priv: 1,
            tmp: 'template_data'
        );

        $array = $user->toArray();

        $this->assertIsArray($array);
        $this->assertEquals('TEST123456', $array['sn']);
        $this->assertEquals('001', $array['pin']);
        $this->assertEquals('John Doe', $array['nama']);
    }

    /** @test */
    public function it_handles_optional_fields_in_easylink_user()
    {
        $data = [
            'sn' => 'TEST123456',
            'pin' => '001',
            'nama' => 'John Doe'
        ];

        $user = EasylinkUser::fromArray($data);

        $this->assertEquals('0', $user->pwd);
        $this->assertEquals('', $user->rfid);
        $this->assertEquals(0, $user->priv);
        $this->assertNull($user->tmp);
    }
}
