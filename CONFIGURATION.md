# Laravel EasyLink SDK Configuration Guide

This guide explains how to configure the Laravel EasyLink SDK package for communicating with Fingerspot EasyLink devices.

## Environment Variables

The package uses environment variables for configuration. Copy the `.env.example` file to `.env` and update the values according to your setup.

### Required Configuration

#### EasyLink SDK Settings

```env
# EasyLink Device Host URL
EASYLINK_SDK_HOST=http://*************:8080

# EasyLink Device Serial Number  
EASYLINK_SDK_SN=Fio616231022480092
```

**EASYLINK_SDK_HOST**: The base URL of your Fingerspot EasyLink device. This should include the protocol (http/https) and port number.

**EASYLINK_SDK_SN**: The serial number of your Fingerspot device. This is usually found on the device label or in the device settings.

#### Database Configuration

```env
# Database settings for fingerspot data
EASYLINK_DB_HOST=localhost
EASYLINK_DB_PORT=3306
EASYLINK_DB_DATABASE=fingerspot
EASYLINK_DB_USERNAME=your_username
EASYLINK_DB_PASSWORD=your_password
```

### Optional Configuration

```env
# Database connection URL (overrides individual settings)
EASYLINK_DB_URL=mysql://username:password@host:port/database

# Unix socket for local connections
EASYLINK_DB_SOCKET=/var/run/mysqld/mysqld.sock

# SSL Certificate for MySQL connections
MYSQL_ATTR_SSL_CA=/path/to/ca-cert.pem
```

## Publishing Configuration

To publish the configuration file to your Laravel application:

```bash
php artisan vendor:publish --provider="Kangangga\EasylinkSdk\EasylinkSdkServiceProvider" --tag="config"
```

This will create `config/laravel-easylink.php` where you can customize the settings.

## Usage Examples

### Basic Usage

```php
use Kangangga\EasylinkSdk\EasylinkSdk;
use Kangangga\EasylinkSdk\Dto\EasylinkUser;

// Create SDK instance
$sdk = new EasylinkSdk('http://*************:8080', 'Fio616231022480092');

// Or use the facade
use EasylinkSdk;
$deviceInfo = EasylinkSdk::device();
```

### User Management

```php
// Create a new user
$user = new EasylinkUser(
    sn: 'Fio616231022480092',
    pin: '001',
    nama: 'John Doe',
    pwd: 'password123',
    rfid: 'RFID123',
    priv: 1,
    tmp: null
);

$response = $sdk->userSet($user);

// Delete users
$response = $sdk->userDel();
```

### Attendance Logs

```php
// Get all scan logs
$logs = $sdk->scanlogAll();

// Get new scan logs only
$newLogs = $sdk->scanlogNew();
```

### Device Information

```php
// Get device information
$deviceInfo = $sdk->device();
```

## Database Models

The package includes Eloquent models for working with fingerspot database:

```php
use Kangangga\EasylinkSdk\Models\Device;
use Kangangga\EasylinkSdk\Models\Pegawai;
use Kangangga\EasylinkSdk\Models\AttLog;

// Get all devices
$devices = Device::all();

// Get employee data
$employees = Pegawai::with('detail')->get();

// Get attendance logs
$attendance = AttLog::whereDate('scan_date', today())->get();
```

## Troubleshooting

### Connection Issues

1. **Check device IP and port**: Ensure the device is accessible at the configured URL
2. **Verify serial number**: Make sure the serial number matches your device
3. **Network connectivity**: Test if you can ping the device from your server

### Database Issues

1. **Check credentials**: Verify database username and password
2. **Database exists**: Ensure the fingerspot database exists
3. **Permissions**: Make sure the user has proper permissions on the database

### Common Errors

- **Connection timeout**: Check firewall settings and network connectivity
- **Authentication failed**: Verify device serial number and credentials
- **Database connection failed**: Check database configuration and credentials

## Testing

Run the package tests to verify your configuration:

```bash
# Run all tests
composer test

# Run specific test suites
vendor/bin/phpunit tests/UnitTest.php
vendor/bin/phpunit tests/EasylinkSdkHttpTest.php
vendor/bin/phpunit tests/ServiceProviderTest.php
vendor/bin/phpunit tests/ModelsTest.php
```

## Security Considerations

1. **Environment Variables**: Never commit `.env` files to version control
2. **Database Credentials**: Use strong passwords and limit database user permissions
3. **Network Security**: Consider using HTTPS if supported by your device
4. **Access Control**: Restrict network access to the fingerspot device

## Support

For issues and questions:
- Check the package documentation
- Review the test files for usage examples
- Open an issue on the GitHub repository
